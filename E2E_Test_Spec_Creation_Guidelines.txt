================================================================================
                    E2E TEST SPEC CREATION GUIDELINES
                         Matrix Booking Neo Tests
================================================================================

OVERVIEW
--------
This guide provides best practices for creating E2E test specifications that work
correctly on the first run with minimal debugging required.

================================================================================
1. INITIAL INFORMATION GATHERING
================================================================================

ALWAYS PROVIDE UPFRONT:
- COA Requirements: Clear GIVEN/WHEN/THEN format with specific pre-requisites
- Base Template: Reference existing similar spec file 
  Example: "based off 06_1 - End to End - Booking.spec.js"
- File Naming: Specify exact naming convention
  Example: "06_13 - End to End - Edit Booking - Change Owner.spec.js"
- Context Directories: Point to relevant folders
  Example: @`e2e-neo\pages/`, @`test-data\neo/`

QUICK REQUEST TEMPLATE:
Create a new spec: [Number] - [Name]
COA: [GIVEN/WHEN/THEN requirements]
Based on: @`existing-spec-file`
Context: @`e2e-neo\pages/` @`test-data\neo/`
Use: [specific user types, page objects, etc.]

================================================================================
2. SPEC STRUCTURE REQUIREMENTS
================================================================================

STANDARD PATTERN:
```javascript
//import test and expect functionality 
import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('Test Name', { 
    tag: '@serial', 
    annotation: {type:'coa', description:'Description'} 
}, async ({ page, loginPage, searchResultsPage, newBookingModalPage, myBookingsPage }) => {
    
    test.slow(); //Sets the timeout to triple the project timeout.
    
    // Test implementation
});
```

================================================================================
3. PAGE OBJECT MODEL USAGE (CRITICAL)
================================================================================

ALWAYS USE PAGE OBJECTS INSTEAD OF DIRECT PAGE INTERACTIONS:

❌ DON'T DO:
```javascript
await page.getByRole('button', { name: 'Book Basement Room 2' }).click();
await page.getByLabel('start-time').fill(time);
await page.getByLabel('Title').fill(title);
await page.getByRole('button', { name: 'Book', exact: true }).click();
```

✅ DO:
```javascript
await searchResultsPage.bookResource('Basement Room 2');
await newBookingModalPage.setStartTime(bookingFromTime);
await newBookingModalPage.setTitle(bookingTitle);
await newBookingModalPage.completeBooking();
```

PREFERRED PAGE OBJECT METHODS:
- searchResultsPage.bookResource(resourceName)
- newBookingModalPage.setStartTime(), setTitle(), setNotes(), completeBooking()
- myBookingsPage.editBooking(), cancelBooking(), verifyBookingExists()
- newBookingModalPage.goToBookingsList(projectViewport.width)

================================================================================
4. TEST DATA MANAGEMENT
================================================================================

STANDARD PATTERN:
```javascript
const locationmanager_user_data = require('../test-data/neo/Users/<USER>');
const standard_user_data = require('../test-data/neo/Users/<USER>');
const org = require('../test-data/neo/Seed.json');

// Dynamic test data with timestamps
const timeNow = dayjs();
const bookingFromTime = timeNow.add(2,'hour');
const bookingToTime = bookingFromTime.add(1,'hour');
const bookingTitle = `Test - Made by ${user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
```

USER PERMISSION LEVELS:
- Location Manager: locationmanager_user.json (minimum for change owner)
- Admin: admin_user.json (highest permissions)
- Standard: standard_user.json (basic user)
- Email: email_user.json (for email testing)

================================================================================
5. RESPONSIVE DESIGN HANDLING
================================================================================

ALWAYS INCLUDE VIEWPORT-AWARE LOGIC:
```javascript
if (projectViewport.width < 768) {
    // Mobile specific actions
    await page.getByRole('button', { name: 'Add attendees Add attendees' }).click();
    await page.getByRole('combobox', { name: 'Search by name or email' }).fill(name);
    await page.getByRole('button', { name: 'Done' }).click();
} else {
    // Desktop specific actions
    await page.getByPlaceholder('Search by name or email address').fill(name);
    await page.getByRole('option', {name: userName}).click();
}
```

================================================================================
6. TEST STEP STRUCTURE
================================================================================

USE DESCRIPTIVE test.step() BLOCKS:
```javascript
await test.step('Create initial booking with ALL possible attributes', async () => {
await test.step('Edit booking - Click Edit booking button', async () => {
await test.step('Verify changes are saved by editing the booking again', async () => {
await test.step('Clean up - Cancel booking', async () => {
```

================================================================================
7. VALIDATION PATTERNS
================================================================================

ALWAYS VERIFY:
- Modal opens/closes correctly
- Data persistence (re-edit to confirm changes)
- All attributes modified (comprehensive validation)
- Proper cleanup (cancel bookings)

EXAMPLE VALIDATION:
```javascript
// Verify modal closes with success
await expect(page.getByRole('heading', { name: 'Room booked' })).toBeVisible();
await expect(newBookingModalPage.bookingSuccessIcon).toBeVisible();

// Verify persistence by re-editing
await myBookingsPage.editBooking(startTime, endTime, title, projectViewport.width);
await expect(page.getByLabel('Title')).toHaveValue(expectedTitle);
```

================================================================================
8. COMMON FIXES TO EXPECT
================================================================================

BE PREPARED FOR:
- Selector conflicts: Use specific locators (e.g., within parent sections)
- Page object updates: May need to fix selectors in page objects
- Responsive issues: Different behavior across viewport sizes
- Timing issues: Add proper waits and state checks

EXAMPLE FIX:
```javascript
// Before (too generic)
this.changeOwnerButton = page.getByRole('button', { name: 'Change' });

// After (specific to section)
this.changeOwnerButton = page.getByRole('heading', { name: 'Booking owner' })
    .locator('..').getByRole('button', { name: 'Change' });
```

================================================================================
9. RUNNING TESTS
================================================================================

STANDARD COMMAND FORMAT:
npx playwright test "e2e-neo\[filename].spec.js" --project=e2e-neo-medium --headed --no-deps

AVAILABLE PROJECTS:
- e2e-neo (desktop)
- e2e-neo-medium (tablet)
- e2e-neo-small (mobile)

================================================================================
10. FILE MANAGEMENT
================================================================================

SPECIFY:
- Exact file numbering (e.g., "change it to 06_13")
- Remove old files if renaming
- Follow established naming conventions
- Use numbered sequence pattern (06_1, 06_2, 06_13, etc.)

================================================================================
11. ERROR PREVENTION CHECKLIST
================================================================================

COMMON ISSUES TO AVOID:
□ Missing page object imports in test parameters
□ Using direct page interactions instead of page object methods
□ Forgetting responsive design handling
□ Not using proper test data patterns
□ Missing cleanup steps
□ Incorrect file numbering
□ Missing test.slow() for complex tests
□ Not handling viewport-specific UI differences

================================================================================
12. BEST PRACTICES SUMMARY
================================================================================

1. Always use page object methods over direct page interactions
2. Include responsive design handling for all UI interactions
3. Use dynamic test data with timestamps for uniqueness
4. Implement comprehensive validation including persistence checks
5. Follow established naming and numbering conventions
6. Include proper cleanup in all tests
7. Use descriptive test.step() blocks for better reporting
8. Specify exact requirements upfront to minimize iterations

================================================================================
END OF GUIDELINES
================================================================================
