import dayjs from 'dayjs';
import { expect } from 'playwright/test';

/**
 * Page Object Model for the booking creation/editing page
 * @class
 * @param {import ('@playwright/test').Page} page
 */
export class NewBookingModalPage {
  constructor(page) {
    this.page = page;
    this.modalHeading = page.getByLabel('New booking', { exact: true });
    this.modalHeadingEdit = page.getByLabel('Edit booking', { exact: true });
    this.startTimeField = page.getByLabel('start-time');
    this.endTimeField = page.getByLabel('end-time');
    this.titleField = page.getByLabel('Title');
    this.notesField = page.getByLabel('Notes');
    this.saveButton = page.getByRole('button', { name: 'Save changes', exact: true });
    this.editButton = page.getByRole('button', { name: 'Edit booking', exact: true });
    this.bookButton = page.getByRole('button', { name: 'Book', exact: true });
    this.updateButton = page.getByRole('button', { name: 'Update', exact: true });
    this.cancelButton = page.getByRole('button', { name: 'Cancel', exact: true });
    this.closeButton = page.getByRole('button', { name: 'Close panel' });
    this.closeBookingConfirmationButton = page.getByRole('button', { name: 'Close booking confirmation' })

    this.repeatBookingCheckbox = page.getByLabel('Repeat booking');
    this.repeatFrequencyButton = page.getByRole('button', { name: 'Repeat frequency' });
    // Desktop uses ID selector, mobile uses button role
    this.mobileRepeatUntilField = page.locator('#repeatEndDate-field');
    this.desktopRepeatUntilField = page.getByRole('button', { name: 'Repeat until Select end date' });

    this.dailyOption = page.getByRole('option', { name: 'Daily' });
    this.everyWeekdayOption = page.getByRole('option', { name: 'Every week day' });
    this.weeklyOption = page.getByRole('option', { name: 'Weekly' });
    this.biWeeklyOption = page.getByRole('option', { name: 'Bi-weekly' });
    this.monthlyOption = page.getByRole('option', { name: 'Monthly' });
    this.mobileEveryWeekdayOption = page.getByRole('button', { name: 'Every week day' });

    this.attendeesSearchField = page.getByPlaceholder('Search by name or email address');
    this.createNewAttendeeButton = page.getByRole('button', { name: 'Create new attendee Create' });
    this.mobileAddAttendeesButton = page.getByRole('button', { name: 'Add attendees Add attendees' });
    this.mobileSearchField = page.getByRole('combobox', { name: 'Search by name or email' });
    this.mobileDoneButton = page.getByRole('button', { name: 'Done' });

    //Attendee details modal (create attendee during booking)
    this.adFirstnameField =  page.getByRole('textbox', { name: 'First name *' });
    this.adLastnameField = page.getByRole('textbox', { name: 'Last name *' });
    this.adCompanyField = page.getByLabel('Company');
    this.adEmailField = page.getByLabel('Email');
    this.adPhoneNumField = page.getByRole('textbox', { name: 'Phone number' });
    this.adCarRegField = page.getByRole('textbox', { name: 'Car registration' });

    //Change booking owner
    this.changeOwnerButton = page.getByRole('button', { name: 'Change' });
    this.changeOwnerSearchField = page.getByRole('heading', { name: 'Booking owner' }).locator('..').getByPlaceholder('Search by name or email address');
    this.changeOwnerCancel = page.getByRole('button', { name: 'Cancel' }).first();

    //Options
    this.cateringButton = page.getByRole('button', { name: 'Catering', exact: true });
    this.equipmentButton = page.getByRole('button', { name: 'Equipment', exact: true });
    this.servicesButton = page.getByRole('button', { name: 'Services', exact: true });
    this.roomLayoutButton = page.getByRole('button', { name: 'Room layout', exact: true });

    // Success message elements
    this.bookingSuccessHeading = page.getByRole('heading', { name: /booked$/ });
    this.bookingSuccessIcon = page.getByRole('img', { name: 'Booking successful' });
    this.repeatBookingCreatedText = page.getByText('Repeats every');

    // Buttons in the success dialog - handle both desktop and mobile views
    this.bookingsButton = page.getByRole('button', { name: 'Bookings' });
    this.mobileMenuButton = page.getByRole('button', { name: 'Menu Access menu options' });
    this.mobileBookingsLink = page.getByRole('link', { name: 'Menu item: Bookings' }); this.makeAnotherBookingButton = page.getByRole('button', { name: 'Make another booking' });
    this.ownerSection = page.locator('div').filter({ hasText: /^Owner$/ }).last();
  }

  async setStartTime(startTime) {
    await this.startTimeField.click();
    await this.startTimeField.fill(startTime.format('h:mm A'));
    await this.page.keyboard.press('Enter');
  }

  async setEndTime(endTime) {
    await this.endTimeField.click();
    await this.endTimeField.fill(endTime.format('h:mm A'));
    await this.page.keyboard.press('Enter');
  }

  async setTitle(title) {
    await this.titleField.click();
    await this.titleField.fill(title);
  }

  async setNotes(notes) {
    await this.notesField.click();
    await this.notesField.fill(notes);
  }
  async setupRepeatBooking(frequency, untilDate, viewportWidth) {
    await this.repeatBookingCheckbox.click();
    await this.repeatFrequencyButton.click();

    if (viewportWidth < 768) {
      if (frequency === 'Every week day') {
        await this.mobileEveryWeekdayOption.click();
      } else {
        await this.page.getByRole('button', { name: frequency }).click();
      }
      await this.mobileRepeatUntilField.click();
    } else {
      await this.page.getByRole('option', { name: frequency }).click();
      await this.desktopRepeatUntilField.click();
    }

    // Get current visible month
    const targetMonth = untilDate.format('MMMM YYYY');
    let currentMonth = await this.page.getByText(targetMonth, { exact: true }).count();

    // Keep clicking next until we find our target month
    while (currentMonth === 0) {
      await this.page.getByRole('button', { name: 'Next month' }).click();
      await this.page.waitForTimeout(200); // Small wait for calendar to update
      currentMonth = await this.page.getByText(targetMonth, { exact: true }).count();
    }

    // Now select the day
    const targetDay = untilDate.format('D');
    await this.page.locator(`button[name="day"]:not(.day-outside)`)
      .filter({ hasText: targetDay, exact: true })
      .first()
      .click({ timeout: 5000 });


    if (viewportWidth < 768) {
      // Mobile view
      await this.page.getByRole('button', { name: 'close' }).click();
    }
  }

  /**
  * Add an internal attendee to the booking
  * @param {string} attendeeName - The name or part of the name of the attendee
  * @param {number} viewportWidth - The current viewport width
  */
  async addInternalAttendee(attendeeName, viewportWidth) {
    if (viewportWidth < 768) {
      // Mobile view
      await this.mobileAddAttendeesButton.click();
      await this.mobileSearchField.fill(attendeeName);
      await this.page.getByRole('option', { name: new RegExp(attendeeName, 'i') }).click();
      await this.mobileDoneButton.click();
    } else {
      // Desktop view
      await this.attendeesSearchField.fill(attendeeName);
      await this.page.getByRole('option', { name: new RegExp(attendeeName, 'i') }).click();
    }
  }

  /**
   * Add an external attendee to the booking, creating a new contact if needed
   * @param {string} email - The email address of the external attendee
   * @param {string} firstName - The first name for the new contact
   * @param {string} lastName - The last name for the new contact
   * @param {number} viewportWidth - The current viewport width
   */  async addExternalAttendee(email, firstName, lastName, viewportWidth) {
    if (viewportWidth < 768) {
      await this.mobileAddAttendeesButton.click();
      await this.page.getByRole('button', { name: 'Create new attendee Create' }).click();
      await this.page.getByRole('textbox', { name: 'First name *' }).fill(firstName);
      await this.page.getByRole('textbox', { name: 'Last name *' }).fill(lastName);
      await this.page.getByLabel('Email').fill(email);
      await this.page.waitForTimeout(500);
      await this.page.getByRole('button', { name: 'Done' }).click();
      await expect(this.page.getByText('Search for attendee')).toBeVisible();
      await this.page.waitForTimeout(2000);
      await this.page.getByRole('button', { name: 'Done' }).click();
    } else {
      await this.page.getByRole('button', { name: 'Create new attendee Create' }).click();
      await this.page.locator('#firstName-field').fill(firstName);
      await this.page.locator('#lastName-field').fill(lastName);
      await this.page.locator('#email-field').fill(email);
      await this.page.waitForTimeout(200);
      await this.page.getByRole('button', { name: 'Done' }).click();
    }
  }

  /**
   * Complete the booking process by clicking the Book button and verifying success
   */  async completeBooking() {
    await this.page.getByRole('button', { name: 'Book', exact: true }).click();
    await this.page.waitForSelector('role=heading[name=/booked$/]');
  }

  async verifyBookingSuccess(startTime, _endTime, ownerName, isRepeat = false) {
    await this.bookingSuccessIcon.waitFor({ state: 'visible' });
    const dateText = startTime.format('ddd, D MMM');
    await this.page.locator('div.border-border-decoration').filter({ hasText: 'Time' })
      .getByText(dateText, { exact: false }).waitFor({ state: 'visible' });
    await this.page.getByText('Owner').locator('..').getByText(ownerName).waitFor({ state: 'visible' });

    if (isRepeat) {
      await this.repeatBookingCreatedText.waitFor({ state: 'visible' });
    }
  }

  /**
   * Navigate to the bookings list after successful booking
   * @param {number} viewportWidth - The current viewport width
   */
  async goToBookingsList(viewportWidth) {
    if (viewportWidth < 768) {
      // Mobile view - need to click the menu button first, then the bookings link
      await this.closeButton.click();
      await this.mobileMenuButton.click();
      await this.mobileBookingsLink.click();
    } else {
      // Desktop view - direct bookings button
      await this.bookingsButton.click();
    }

    // Wait for the bookings page to load
    await this.page.waitForLoadState('domcontentloaded');
    await this.page.waitForTimeout(1000); // Additional wait to ensure UI is fully loaded
  }
}

