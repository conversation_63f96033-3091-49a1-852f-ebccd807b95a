//import test and expect functionality 
import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('End to end - Edit single booking', { tag: '@serial', annotation:{type:'coa', description:'End to end - Edit single booking with all attributes'} }, async ({ page, loginPage, searchResultsPage, newBookingModalPage, myBookingsPage }) => {
    
    test.slow(); //Sets the timeout to triple the project timeout. This is due to accessing the outlook webmail UI.

    const email_user_data = require('../test-data/neo/Users/<USER>');
    const standard_user_data = require('../test-data/neo/Users/<USER>');
    const org = require('../test-data/neo/Seed.json');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(email_user_data);
    });

    //Define details for a new booking with ALL possible attributes
    const timeNow = dayjs();
    const bookingFromTime = timeNow.add(2,'hour')
    const bookingToTime = bookingFromTime.add(1,'hour');
    const bookingTitle = `Test booking title - Made by ${email_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
    const bookingNotes = `Test booking notes - Made by ${email_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
    
    await test.step('Create initial booking with ALL possible attributes', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
        // Book Basement Room 2
        await page.getByRole('button', { name: 'Book Basement Room 2' }).click();
        
        // Set time
        await page.getByLabel('start-time').click();
        await page.getByLabel('start-time').fill(`${bookingFromTime.format('h:mm A')}`);
        await page.keyboard.press('Enter');
        
        // Add title
        await page.getByLabel('Title').click();
        await page.getByLabel('Title').fill(bookingTitle);
        
        // Add notes
        await page.getByLabel('Notes').click();
        await page.getByLabel('Notes').fill(bookingNotes);
        
        // Add catering option
        await page.getByRole('button', { name: 'Catering' }).click();
        await page.getByRole('button', { name: 'Add catering Add catering' }).click();
        await page.getByRole('button', { name: 'Option Select catering' }).click();
        
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: 'Catering option 2' }).click();
        } else {
            await page.getByRole('option', { name: 'Catering option 2' }).click();
        }
        
        // Add equipment option
        await page.getByRole('button', { name: 'Equipment' }).click();
        await page.getByRole('button', { name: 'Add equipment Add equipment' }).click();
        await page.getByRole('button', { name: 'Option Select equipment' }).click();
   
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: 'Equipment option 2' }).click();
        } else {
            await page.getByRole('option', { name: 'Equipment option 2' }).click();
        }
        
        // Add services option
        await page.getByRole('button', { name: 'Services' }).click();
        await page.getByRole('button', { name: 'Add service Add service' }).click();
        await page.getByRole('button', { name: 'Option Select service' }).click();

        if (projectViewport.width < 768){
            await page.getByRole('button', { name: 'Services option 2' }).click();
        } else {
            await page.getByRole('option', { name: 'Services option 2' }).click();
        }
        
        // Add room layout
        await page.getByRole('button', { name: 'Room layout' }).click();
        await page.getByLabel('Cabaret').click();
        
        // Add internal attendee (Standard User)
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: 'Add attendees Add attendees' }).click();
            await page.getByRole('combobox', { name: 'Search by name or email' }).fill(standard_user_data.FirstName);
            await page.getByRole('option', {name: standard_user_data.Name}).click();
            await page.getByRole('button', { name: 'Done' }).click();
        } else {
            await page.getByPlaceholder('Search by name or email address').fill(standard_user_data.FirstName);
            await page.getByRole('option', {name: standard_user_data.Name}).click();
        }
        
        // Add external attendee (Visitor Contact)
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: 'Add attendees Add attendees' }).click();
            await page.getByRole('combobox', { name: 'Search by name or email' }).fill(`Visitor${org.seed}`);
            await page.getByRole('option', {name: `Visitor${org.seed}`}).click();
            await page.getByRole('button', { name: 'Done' }).click();
        } else {
            await page.getByPlaceholder('Search by name or email address').fill(`Visitor${org.seed}`);
            await page.getByRole('option', {name: `Visitor${org.seed}`}).click();
        }
        
        // Complete booking
        await page.getByRole('button', { name: 'Book', exact: true }).click();
        await expect(page.getByRole('heading', { name: 'Basement Room 2 booked' })).toBeVisible();
        
        // Navigate to bookings list
        await newBookingModalPage.goToBookingsList(projectViewport.width);
    });

    // Define new values for editing ALL attributes
    const editedTitle = `EDITED - ${bookingTitle}`;
    const editedNotes = `EDITED - ${bookingNotes}`;
    const editedFromTime = bookingFromTime.add(30, 'minutes');
    const editedToTime = bookingToTime.add(30, 'minutes');

    await test.step('Edit booking - Click Edit booking button', async () => {
        await myBookingsPage.editBooking(bookingFromTime, bookingToTime, bookingTitle, projectViewport.width);
        await expect(newBookingModalPage.modalHeadingEdit).toContainText('Edit booking');
    });

    await test.step('Modify ALL attributes of the booking', async () => {
        // Change start and end time
        await newBookingModalPage.setStartTime(editedFromTime);
        await newBookingModalPage.setEndTime(editedToTime);
        
        // Change title
        await newBookingModalPage.setTitle(editedTitle);
        
        // Change notes
        await newBookingModalPage.setNotes(editedNotes);
        
        // Change catering option
        await page.getByRole('button', { name: 'Catering (1)' }).click();
        await page.getByRole('button', { name: 'Option Catering option 2' }).click();
        
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: 'Catering option 1' }).click();
        } else {
            await page.getByRole('option', { name: 'Catering option 1' }).click();
        }
        
        // Change equipment option
        await page.getByRole('button', { name: 'Equipment (1)' }).click();
        await page.getByRole('button', { name: 'Option Equipment option 2' }).click();
        
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: 'Equipment option 1' }).click();
        } else {
            await page.getByRole('option', { name: 'Equipment option 1' }).click();
        }
        
        // Change services option
        await page.getByRole('button', { name: 'Services (1)' }).click();
        await page.getByRole('button', { name: 'Option Services option 2' }).click();
        
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: 'Services option 1' }).click();
        } else {
            await page.getByRole('option', { name: 'Services option 1' }).click();
        }
        
        // Change room layout
        await page.getByRole('button', { name: 'Room layout' }).click();
        await page.getByLabel('Buffet').click();
        
        // Remove one attendee and add a new external attendee
        await page.getByRole('button', { name: 'Standard Test Automation User' }).click();
        
        // Add a new external attendee
        const randomNumber = Math.floor(Math.random() * 9999);
        const newVisitorEmail = `newvisitor${randomNumber}@test.com`;
        
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: 'Add attendees Add attendees' }).click();
            await page.getByRole('button', { name: 'Create new attendee Create' }).click();
            await page.getByRole('textbox', { name: 'First name *' }).fill('New');
            await page.getByRole('textbox', { name: 'Last name *' }).fill('Visitor');
            await page.getByLabel('Email').fill(newVisitorEmail);
            await page.waitForTimeout(500);
            await page.getByRole('button', { name: 'Done' }).click();
            await expect(page.getByText('Search for attendee')).toBeVisible();
            await page.waitForTimeout(2000);
            await page.getByRole('button', { name: 'Done' }).click();
        } else {
            await page.getByRole('button', { name: 'Create new attendee Create' }).click();
            await page.locator('#firstName-field').fill('New');
            await page.locator('#lastName-field').fill('Visitor');
            await page.locator('#email-field').fill(newVisitorEmail);
            await page.waitForTimeout(200);
            await page.getByRole('button', { name: 'Done' }).click();
        }
    });

    await test.step('Save changes and verify modal closes', async () => {
        await newBookingModalPage.saveButton.click();
        await expect(page.getByRole('heading', { name: 'Basement Room 2 booked' })).toBeVisible();
        await expect(newBookingModalPage.bookingSuccessIcon).toBeVisible();
        
        // Navigate back to bookings list
        await newBookingModalPage.goToBookingsList(projectViewport.width);
    });

    await test.step('Verify changes are saved by editing the booking again', async () => {
        // Edit the booking again to verify changes were saved
        await myBookingsPage.editBooking(editedFromTime, editedToTime, editedTitle, projectViewport.width);
        
        // Verify all changes were saved
        await expect(page.getByLabel('start-time')).toHaveValue(`${editedFromTime.format('h:mm A')}`);
        await expect(page.getByLabel('end-time')).toHaveValue(`${editedToTime.format('h:mm A')}`);
        await expect(page.getByLabel('Title')).toHaveValue(editedTitle);
        await expect(page.getByLabel('Notes')).toHaveValue(editedNotes);
        
        // Verify attendees
        await expect(page.getByLabel('Number of attendees')).toHaveValue('3');
        await expect(page.getByRole('button', { name: 'Email Test Automation User (you) (owner)' })).toBeVisible();
        await expect(page.getByRole('button', { name: 'New Visitor' })).toBeVisible();
        await expect(page.getByRole('button', { name: 'Visitor Contact' })).toBeVisible();
        
        // Verify options were changed
        await expect(page.getByRole('button', { name: 'Services (1)' })).toBeVisible();
        await page.getByRole('button', { name: 'Services (1)' }).click();
        await expect(page.getByRole('button', { name: 'Option Services option 1' })).toBeVisible();
        
        await page.getByRole('button', { name: 'Room layout' }).click();
        await expect(page.getByLabel('Buffet')).toBeChecked();
        
        await expect(page.getByRole('button', { name: 'Catering (1)' })).toBeVisible();
        await page.getByRole('button', { name: 'Catering (1)' }).click();
        await expect(page.getByRole('button', { name: 'Option Catering option 1' })).toBeVisible();
        
        await expect(page.getByRole('button', { name: 'Equipment (1)' })).toBeVisible();
        await page.getByRole('button', { name: 'Equipment (1)' }).click();
        await expect(page.getByRole('button', { name: 'Option Equipment option 1' })).toBeVisible();
    });

    await test.step('Clean up - Cancel booking', async () => {
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: 'Cancel booking' }).click();
        } else {
            await page.getByLabel('Cancel', { exact: true }).click();
            await page.getByRole('button', { name: `Cancel booking ${editedFromTime.format('h:mm A ddd DD')}` }).click();
        }
        
        await page.getByRole('button', { name: 'Cancel booking' }).click();
    });
    
});
