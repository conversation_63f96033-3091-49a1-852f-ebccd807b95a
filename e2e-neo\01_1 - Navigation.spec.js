//import test and expect functionality 
import { test, expect } from './fixtures/pages.fixture';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('Check the navigation bar or panels', { tag: '@parallel', annotation:{type:'coa', description:'Menu nav element'} }, async ({ baseURL, page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Check the navigation bar or slide out', async () => {
        await expect(page.getByRole('img', { name: 'Matrix Booking logo' })).toBeVisible();
        await expect(page.locator('div').filter({ hasText: 'Set default location New' }).first()).toBeVisible();
        if (projectViewport.width < 1024){
            await expect.soft(page.locator('div').filter({ hasText: 'Set default location New' }).first()).toHaveScreenshot('Neo-navigationMobileBarClosed.png');
            await page.getByRole('button', { name: 'Menu Access menu options' }).click();
        }
        await expect(page.getByRole('link', { name: 'Menu item: New booking' })).toBeVisible();
        await expect(page.getByRole('link', { name: 'Menu item: Bookings' })).toBeVisible();
        await expect(page.getByRole('button', { name: 'ST Access log out button' })).toBeVisible();
        await expect(page).toHaveURL(`${baseURL}neo/search`);
    });
    
    await test.step('Compare navigation bar against a golden file', async () => {
         if (projectViewport.width < 1024){
            await expect.soft(page.locator('div').filter({ hasText: 'Set default location New' }).first()).toHaveScreenshot('Neo-navigationMobileBarOpen.png');
            await expect.soft(page).toHaveScreenshot('Neo-navigationMobileBarOpen2.png');
        } else {
            await expect.soft(page.locator('div').filter({ hasText: 'Set default location New' }).first()).toHaveScreenshot('Neo-navigationBar.png');
        }
    });

});

test('Use the nav bar links', { tag: '@parallel', annotation:{type:'coa', description:'Menu nav element'} }, async ({ baseURL, page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Click the Bookings link in the nav bar', async () => {
        if (projectViewport.width < 1024){
            await page.getByRole('button', { name: 'Menu Access menu options' }).click();
        }
        await page.getByRole('link', { name: 'Menu item: Bookings' }).click();
        await expect(page).toHaveURL(`${baseURL}neo/my-bookings`);
    });
    
    await test.step('Click the New booking link in the nav bar', async () => {
        if (projectViewport.width < 1024){
            await page.getByRole('button', { name: 'Menu Access menu options' }).click();
        }
        await page.getByRole('link', { name: 'Menu item: New booking' }).click();
        await expect(page).toHaveURL(`${baseURL}neo/search`);
    });
    
});

test('Check the user details & logout option in the nav bar', { tag: '@parallel', annotation:{type:'coa', description:'Menu nav element'} }, async ({ baseURL, page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Click the user icon or open the menu nav & check the details shown', async () => {
        if (projectViewport.width < 1024){
            await page.getByRole('button', { name: 'Menu Access menu options' }).click();
            await expect(page.getByRole('button', { name: 'ST Access log out button' })).toBeVisible();
            await expect(page.locator('[id="headlessui-popover-panel-\\:r1r\\:"]').getByText(`${standard_user_data.Name}`)).toBeVisible();
            await expect(page.locator('[id="headlessui-popover-panel-\\:r1r\\:"]').getByText(`${standard_user_data.Email}`)).toBeVisible();
        } else {
            await page.getByRole('button', { name: 'ST Access log out button' }).click();
            await expect(page.getByRole('button', { name: 'ST Access log out button' }).nth(1)).toBeVisible();
        }
        await expect(page.getByRole('link', { name: 'Menu item: Log out' })).toBeVisible();
    });
    
    await test.step('Compare the user details against a golden file', async () => {
         if (projectViewport.width < 1024){
            await expect.soft(page.locator('[id="headlessui-popover-panel-\\:r1r\\:"]').getByText(`ST Access log out button${standard_user_data.Name}`)).toHaveScreenshot('Neo-navigationBar-userDetails.png');

        } else {
            await expect.soft(page.locator('.text-subtitle').first()).toHaveScreenshot('Neo-navigationBar-userDetails.png');
        }
    });
    
});

test('Check a custom org logo', { tag: '@serial', annotation:{type:'coa', description:'Menu nav element'} }, async ({ baseURL, page, loginPage }) => {

    const admin_user_data = require('../test-data/neo/Users/<USER>');
    const orgData = require(`../test-data/neo/Seed.json`);

     //Please note: utilise the current web app to set the org logo
    await test.step('Login to the current web app & upload a custom logo', async () => {
        await page.goto('ui/#/admin?view=logo-branding');
        await page.getByLabel('Email').fill(admin_user_data.Email);
        await page.getByLabel('Password').fill(admin_user_data.Password);
        await page.getByRole('button', { name: 'Log in' }).click();
        const fileChooserPromise = page.waitForEvent('filechooser');
        await page.getByRole('button', { name: 'Upload' }).first().click();
        const fileChooser = await fileChooserPromise;
        await fileChooser.setFiles('./test-data/Images/NewLogo.png');
        const responsePromise = await page.waitForResponse(response => response.url() == `${baseURL}api/v1/org/${orgData.orgID}/logo` && response.status() === 200);
    });

    await test.step('Navigate to the Neo search page', async () => {
        await page.goto('/neo/search');
        await expect(page.getByRole('heading', { name: 'New booking' })).toBeVisible();
    });

    await test.step('Check the navigation bar or slide out', async () => {
        await expect(page.getByRole('img', { name: 'Organisation logo' })).toBeVisible();
    });
    
    await test.step('Compare navigation bar against a golden file', async () => {
        await expect.soft(page.getByRole('img', { name: 'Organisation logo' })).toHaveScreenshot('Neo-navigationBar-customLogo.png', { maxDiffPixelRatio: 0.15 });
    });

    await test.step('Swap back to the current web app & remove the custom logo', async () => {
        await page.goto('ui/#/admin?view=logo-branding'); 
        await page.getByRole('button', { name: 'Remove' }).click();
        await page.waitForResponse(response => response.url() == `${baseURL}api/v1/org/${orgData.orgID}/logo?logoKind=MAIN` && response.status() === 204);
    });
    
});